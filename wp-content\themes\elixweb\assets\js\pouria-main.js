jQuery(document).ready(function ($) {
    function runAnimationCycle() {
        const $targets = $('.ex_pyramid svg, .ex_pyramid img');

        // Step 0: Clean up previous state
        $targets.removeClass('active_one active_two active_three active_four active_five active_six');

        $('.ex_pyramid svg').each(function () {
            var $svg = $(this);

            // skip specific classes
            if (
                $svg.hasClass('ex_static_objects') ||
                $svg.hasClass('svg_two_pyramid_up') ||
                $svg.hasClass('svg_three_pyramid_up') ||
                $svg.hasClass('svg_main_pyramid_center') ||
                $svg.hasClass('svg_four_pyramid_up')
            ) {
                return;
            }

            // reset transform completely
            $svg.css('transform', '');
        });

        // Immediately add .active_one (instead of waiting 3s again)
        $targets.each(function () {
            $(this).addClass('active_one');
        });

        // Adds .active_two after 6s
        setTimeout(function () {
            $targets.each(function () {
                $(this).addClass('active_two');
            });
        }, 6000);

        // Rotates and adds .active_three after 6s
        setTimeout(function () {
            $('.ex_pyramid svg').each(function () {
                var $svg = $(this);
                if (
                    $svg.hasClass('ex_static_objects') ||
                    $svg.hasClass('svg_two_pyramid_up') ||
                    $svg.hasClass('svg_three_pyramid_up') ||
                    $svg.hasClass('svg_main_pyramid_center') ||
                    $svg.hasClass('svg_four_pyramid_up')
                ) {
                    return; // skip
                }

                var transform = $svg.css('transform');

                // We'll try to get the original inline style as fallback because
                // sometimes css('transform') returns matrix which is hard to parse back for translate/scale
                // So let's try inline style 'transform' attribute first
                var inlineTransform = $svg.attr('style') || '';
                var matchTransform = inlineTransform.match(/transform:\s*([^;]+);?/);
                var transformStr = matchTransform ? matchTransform[1] : '';

                // If inline transform exists, prefer that
                if (transformStr) {
                    transform = transformStr;
                }

                // Parse the transform string parts, e.g. "rotate(-44deg) translate(-69%, 5%) scale(0.8)"
                // Use regex to find rotate and extract angle
                var rotateMatch = transform.match(/rotate\(([-\d.]+)deg\)/);
                var currentAngle = 0;
                if (rotateMatch) {
                    currentAngle = parseFloat(rotateMatch[1]);
                }

                // New rotation angle minus 7 degrees
                var newAngle = currentAngle - 7;

                // Replace old rotate(...) with new rotate(...)
                var newTransform;
                if (rotateMatch) {
                    // Replace the rotate(...) part only
                    newTransform = transform.replace(/rotate\(([-\d.]+)deg\)/, 'rotate(' + newAngle + 'deg)');
                } else {
                    // If no rotate found, just add rotate(-6deg) in front
                    newTransform = 'rotate(' + newAngle + 'deg) ' + transform;
                }

                $svg.addClass('active_three');
                $svg.css('transform', newTransform);
            });

            // ✅ Clear .active_one transforms after .active_three is done
            $('.ex_pyramid svg').each(function () {
                var $svg = $(this);
                if ($svg.hasClass('active_one')) {
                    $svg.css('transform', '');
                }
            });
        }, 6000);

        // Adds .active_four after 12s
        setTimeout(function () {
            $targets.each(function () {
                $(this).addClass('active_four');
            });
        }, 12000);

        // Adds .active_five after 18s
        setTimeout(function () {
            $targets.each(function () {
                $(this).addClass('active_five');
            });
        }, 18000);

        // Rotates again and adds .active_six after 18s
        setTimeout(function () {
            $('.ex_pyramid svg').each(function () {
                var $svg = $(this);
                if (
                    $svg.hasClass('ex_static_objects') ||
                    $svg.hasClass('svg_two_pyramid_up') ||
                    $svg.hasClass('svg_three_pyramid_up') ||
                    $svg.hasClass('svg_main_pyramid_center') ||
                    $svg.hasClass('svg_four_pyramid_up')
                ) {
                    return; // skip
                }

                var transform = $svg.css('transform');

                // Same logic as above to extract transform
                var inlineTransform = $svg.attr('style') || '';
                var matchTransform = inlineTransform.match(/transform:\s*([^;]+);?/);
                var transformStr = matchTransform ? matchTransform[1] : '';

                if (transformStr) {
                    transform = transformStr;
                }

                var rotateMatch = transform.match(/rotate\(([-\d.]+)deg\)/);
                var currentAngle = 0;
                if (rotateMatch) {
                    currentAngle = parseFloat(rotateMatch[1]);
                }

                var newAngle = currentAngle - 6;

                var newTransform;
                if (rotateMatch) {
                    newTransform = transform.replace(/rotate\(([-\d.]+)deg\)/, 'rotate(' + newAngle + 'deg)');
                } else {
                    newTransform = 'rotate(' + newAngle + 'deg) ' + transform;
                }

                $svg.addClass('active_six');
                $svg.css('transform', newTransform);
            });

            // ✅ Clear .active_four transforms after .active_six is done
            $('.ex_pyramid svg').each(function () {
                var $svg = $(this);
                if ($svg.hasClass('active_four')) {
                    $svg.css('transform', '');
                }
            });
        }, 18000);

        // Final cleanup after 24s
        setTimeout(function () {
            // ✅ Remove all active_* classes and inline styles
            $('.ex_pyramid svg, .ex_pyramid img').each(function () {
                var $el = $(this);
                $el.removeClass('active_one active_two active_three active_four active_five active_six');
                $el.removeAttr('style');
            });

            // ✅ Wait 6s for transition (all 6s ease-in-out) to finish before idle
            setTimeout(function () {
                // 💤 Idle wait: 3 seconds from 30000 to 33000 ms
                setTimeout(function () {
                    runAnimationCycle(); // 🔁 loop again after pause
                }, 3000);
            }, 6000); // 6s pause
        }, 24000); // Cleanup begins at 24s
    }

    // Start the first loop after 3 seconds
    setTimeout(function () {
        runAnimationCycle();
    }, 3000);
});
