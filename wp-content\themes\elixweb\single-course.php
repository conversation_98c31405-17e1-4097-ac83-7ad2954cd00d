<?php
/**
 * Single Course Template - New Design
 * Template for displaying individual course posts based on provided design
 */

if (!defined('ABSPATH'))
    exit;

get_header();

// Get course meta data
$course_id = get_the_ID();
$price = get_post_meta($course_id, '_course_price', true);
$sale_price = get_post_meta($course_id, '_course_sale_price', true);
$is_free = get_post_meta($course_id, '_course_is_free', true);
$duration = get_post_meta($course_id, '_course_duration', true);
$lessons_count = get_post_meta($course_id, '_lessons_count', true);
$instructor = get_post_meta($course_id, '_course_instructor', true);
$course_status = get_post_meta($course_id, '_course_status', true);
$difficulty = get_post_meta($course_id, '_course_difficulty', true);
$language = get_post_meta($course_id, '_course_language', true);
$certificate_enabled = get_post_meta($course_id, '_certificate_enabled', true);
$preview_video = get_post_meta($course_id, '_course_preview_video', true);
$curriculum = get_post_meta($course_id, '_course_curriculum', true);
$video_lessons = get_post_meta($course_id, '_course_video_lessons', true);
$download_files = get_post_meta($course_id, '_course_download_files', true);
$prerequisites = get_post_meta($course_id, '_course_prerequisites', true);
$target_audience = get_post_meta($course_id, '_course_target_audience', true);
$learning_outcomes = get_post_meta($course_id, '_course_learning_outcomes', true);
$course_features = get_post_meta($course_id, '_course_features', true);

// Get taxonomies
$course_categories = get_the_terms($course_id, 'course_category');
$course_levels = get_the_terms($course_id, 'course_level');
$course_tags = get_the_terms($course_id, 'course_tag');

// Calculate price display
$display_price = '';
$discount_percentage = 0;
if ($is_free) {
    $display_price = 'رایگان';
} else {
    if ($sale_price && $sale_price < $price) {
        $discount_percentage = round((($price - $sale_price) / $price) * 100);
        $display_price = number_format($sale_price) . ' تومان';
    } else {
        $display_price = $price ? number_format($price) . ' تومان' : 'تماس بگیرید';
    }
}

// Status labels
$status_labels = [
    'upcoming' => 'به زودی',
    'active' => 'فعال',
    'completed' => 'تکمیل شده'
];

// Difficulty labels
$difficulty_labels = [
    'beginner' => 'مبتدی',
    'intermediate' => 'متوسط',
    'advanced' => 'پیشرفته'
];

// Language labels
$language_labels = [
    'persian' => 'فارسی',
    'english' => 'انگلیسی',
    'arabic' => 'عربی'
];

// Features labels
$features_labels = [
    'lifetime_access' => 'دسترسی مادام‌العمر',
    'mobile_access' => 'دسترسی موبایل',
    'downloadable' => 'قابل دانلود',
    'subtitles' => 'زیرنویس',
    'assignments' => 'تکالیف عملی',
    'community' => 'انجمن دانشجویان',
    'support' => 'پشتیبانی مدرس',
    'updates' => 'به‌روزرسانی رایگان'
];
?>

<main class="course-single-page">
    <div class="ex-container">
        <?php if (have_posts()):
            while (have_posts()):
                the_post(); ?>

                <!-- Course Hero Section -->
                <section class="course-hero">
                    <div class="ex-row">
                        <div class="ex-col-lg-8 ex-col-12">
                            <!-- Course Featured Image -->
                            <div class="course-featured-image">
                                <?php if (has_post_thumbnail()): ?>
                                    <?php the_post_thumbnail('large', ['class' => 'course-main-image']); ?>
                                <?php else: ?>
                                    <div class="no-image-placeholder">
                                        <svg width="120" height="120" viewBox="0 0 24 24" fill="none">
                                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor"
                                                stroke-width="2" />
                                            <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2" />
                                            <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        <p>تصویر شاخص دوره</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Course Title -->
                            <h1 class="course-main-title"><?php the_title(); ?></h1>

                            <!-- Course Short Description -->
                            <div class="course-short-description">
                                <?php the_excerpt(); ?>
                            </div>

                            <!-- Course Meta Grid -->
                            <div class="course-meta-grid">
                                <?php if ($instructor): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">مدرس:</span>
                                        <span class="meta-value"><?php echo esc_html($instructor); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($duration): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">مدت زمان:</span>
                                        <span class="meta-value"><?php echo esc_html($duration); ?> ساعت</span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($lessons_count): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">تعداد جلسات:</span>
                                        <span class="meta-value"><?php echo esc_html($lessons_count); ?> جلسه</span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($difficulty && isset($difficulty_labels[$difficulty])): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">سطح:</span>
                                        <span class="meta-value"><?php echo esc_html($difficulty_labels[$difficulty]); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($language && isset($language_labels[$language])): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">زبان:</span>
                                        <span class="meta-value"><?php echo esc_html($language_labels[$language]); ?></span>
                                    </div>
                                <?php endif; ?>

                                <?php if ($course_status && isset($status_labels[$course_status])): ?>
                                    <div class="meta-item">
                                        <span class="meta-label">وضعیت:</span>
                                        <span
                                            class="meta-value status-<?php echo $course_status; ?>"><?php echo esc_html($status_labels[$course_status]); ?></span>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Course Categories -->
                            <?php if ($course_categories && !is_wp_error($course_categories)): ?>
                                <div class="course-categories">
                                    <?php foreach ($course_categories as $category): ?>
                                        <span class="category-tag"><?php echo esc_html($category->name); ?></span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Course Description -->
                            <div class="course-description">
                                <?php the_excerpt(); ?>
                            </div>
                        </div>
                    </div>

                    <div class="ex-col-lg-4 ex-col-12">
                        <div class="course-sidebar">
                            <!-- Course Preview Video -->
                            <?php if ($preview_video): ?>
                                <div class="course-preview-video">
                                    <div class="video-container">
                                        <?php
                                        // Handle different video URL formats
                                        $video_embed = '';
                                        if (strpos($preview_video, 'youtube.com') !== false || strpos($preview_video, 'youtu.be') !== false) {
                                            $video_id = '';
                                            if (strpos($preview_video, 'youtu.be') !== false) {
                                                $video_id = substr(parse_url($preview_video, PHP_URL_PATH), 1);
                                            } else {
                                                parse_str(parse_url($preview_video, PHP_URL_QUERY), $query);
                                                $video_id = $query['v'] ?? '';
                                            }
                                            if ($video_id) {
                                                $video_embed = "https://www.youtube.com/embed/{$video_id}";
                                            }
                                        } elseif (strpos($preview_video, 'vimeo.com') !== false) {
                                            $video_id = substr(parse_url($preview_video, PHP_URL_PATH), 1);
                                            if ($video_id) {
                                                $video_embed = "https://player.vimeo.com/video/{$video_id}";
                                            }
                                        }

                                        if ($video_embed): ?>
                                            <iframe src="<?php echo esc_url($video_embed); ?>" frameborder="0" allowfullscreen></iframe>
                                        <?php else: ?>
                                            <video controls>
                                                <source src="<?php echo esc_url($preview_video); ?>" type="video/mp4">
                                                مرورگر شما از پخش ویدیو پشتیبانی نمی‌کند.
                                            </video>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php else: ?>
                                <!-- Course Thumbnail -->
                                <div class="course-thumbnail">
                                    <?php if (has_post_thumbnail()): ?>
                                        <?php the_post_thumbnail('large', ['class' => 'course-image']); ?>
                                    <?php else: ?>
                                        <div class="no-image-placeholder">
                                            <svg width="80" height="80" viewBox="0 0 24 24" fill="none">
                                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor"
                                                    stroke-width="2" />
                                                <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2" />
                                                <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2" />
                                            </svg>
                                            <p>تصویر دوره</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Course Pricing -->
                            <div class="course-pricing">
                                <div class="price-container">
                                    <?php if ($is_free): ?>
                                        <span class="price-free">رایگان</span>
                                    <?php else: ?>
                                        <?php if ($sale_price && $sale_price < $price): ?>
                                            <div class="price-sale">
                                                <span class="original-price"><?php echo number_format($price); ?> تومان</span>
                                                <span class="sale-price"><?php echo number_format($sale_price); ?> تومان</span>
                                                <span class="discount-badge"><?php echo $discount_percentage; ?>% تخفیف</span>
                                            </div>
                                        <?php else: ?>
                                            <span class="regular-price"><?php echo $display_price; ?></span>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>

                                <!-- Enrollment Button -->
                                <div class="enrollment-section">
                                    <?php if ($course_status === 'upcoming'): ?>
                                        <button class="btn-enroll btn-upcoming" disabled>
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
                                                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" />
                                            </svg>
                                            به زودی
                                        </button>
                                    <?php elseif ($course_status === 'completed'): ?>
                                        <button class="btn-enroll btn-completed" disabled>
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" />
                                            </svg>
                                            تکمیل شده
                                        </button>
                                    <?php else: ?>
                                        <button class="btn-enroll btn-active">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" />
                                                <path
                                                    d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                                                    stroke="currentColor" stroke-width="2" />
                                            </svg>
                                            <?php echo $is_free ? 'شروع دوره' : 'ثبت نام در دوره'; ?>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Course Features -->
                            <?php if ($course_features && is_array($course_features)): ?>
                                <div class="course-features">
                                    <h3>ویژگی‌های دوره</h3>
                                    <ul class="features-list">
                                        <?php foreach ($course_features as $feature): ?>
                                            <?php if (isset($features_labels[$feature])): ?>
                                                <li class="feature-item">
                                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                        <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" />
                                                    </svg>
                                                    <span><?php echo esc_html($features_labels[$feature]); ?></span>
                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            <?php endif; ?>

                            <!-- Certificate -->
                            <?php if ($certificate_enabled): ?>
                                <div class="course-certificate">
                                    <div class="certificate-info">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path
                                                d="M14 11C14 12.1046 13.1046 13 12 13C10.8954 13 10 12.1046 10 11C10 9.89543 10.8954 9 12 9C13.1046 9 14 9.89543 14 11Z"
                                                stroke="currentColor" stroke-width="2" />
                                            <path
                                                d="M12 1L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 1Z"
                                                stroke="currentColor" stroke-width="2" />
                                        </svg>
                                        <div>
                                            <h4>گواهی تکمیل</h4>
                                            <p>پس از تکمیل دوره گواهی دریافت کنید</p>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
            </div>
            </section>

            <!-- Course Content Tabs -->
            <section class="course-content-tabs">
                <div class="ex-row">
                    <div class="ex-col-12">
                        <div class="tabs-container">
                            <div class="tabs-nav">
                                <button class="tab-btn active" data-tab="description">توضیحات</button>
                                <?php if ($curriculum && !empty($curriculum)): ?>
                                    <button class="tab-btn" data-tab="curriculum">سرفصل‌ها</button>
                                <?php endif; ?>
                                <?php if ($video_lessons && !empty($video_lessons)): ?>
                                    <button class="tab-btn" data-tab="lessons">ویدیوها</button>
                                <?php endif; ?>
                                <?php if ($download_files && !empty($download_files)): ?>
                                    <button class="tab-btn" data-tab="downloads">فایل‌های دانلودی</button>
                                <?php endif; ?>
                                <button class="tab-btn" data-tab="info">اطلاعات تکمیلی</button>
                            </div>

                            <div class="tabs-content">
                                <!-- Description Tab -->
                                <div class="tab-content active" id="description">
                                    <div class="course-full-description">
                                        <?php the_content(); ?>
                                    </div>
                                </div>

                                <!-- Curriculum Tab -->
                                <?php if ($curriculum && !empty($curriculum)): ?>
                                    <div class="tab-content" id="curriculum">
                                        <div class="curriculum-section">
                                            <h3>سرفصل‌های دوره</h3>
                                            <div class="curriculum-list">
                                                <?php foreach ($curriculum as $index => $lesson): ?>
                                                    <div class="curriculum-item">
                                                        <div class="lesson-header">
                                                            <div class="lesson-number"><?php echo $index + 1; ?></div>
                                                            <div class="lesson-info">
                                                                <h4 class="lesson-title"><?php echo esc_html($lesson['title']); ?>
                                                                </h4>
                                                                <div class="lesson-meta">
                                                                    <?php if (!empty($lesson['duration'])): ?>
                                                                        <span class="lesson-duration">
                                                                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                                                                <circle cx="12" cy="12" r="10" stroke="currentColor"
                                                                                    stroke-width="2" />
                                                                                <polyline points="12,6 12,12 16,14" stroke="currentColor"
                                                                                    stroke-width="2" />
                                                                            </svg>
                                                                            <?php echo esc_html($lesson['duration']); ?>
                                                                        </span>
                                                                    <?php endif; ?>
                                                                    <?php if (!empty($lesson['is_free'])): ?>
                                                                        <span class="lesson-free">رایگان</span>
                                                                    <?php endif; ?>
                                                                    <?php if (!empty($lesson['has_quiz'])): ?>
                                                                        <span class="lesson-quiz">دارای آزمون</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($lesson['description'])): ?>
                                                            <div class="lesson-description">
                                                                <p><?php echo esc_html($lesson['description']); ?></p>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Video Lessons Tab -->
                                <?php if ($video_lessons && !empty($video_lessons)): ?>
                                    <div class="tab-content" id="lessons">
                                        <div class="video-lessons-section">
                                            <h3>ویدیوهای دوره</h3>
                                            <div class="video-lessons-list">
                                                <?php foreach ($video_lessons as $index => $video): ?>
                                                    <div class="video-lesson-item">
                                                        <div class="video-info">
                                                            <div class="video-thumbnail">
                                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                    <polygon points="5,3 19,12 5,21" stroke="currentColor"
                                                                        stroke-width="2" fill="currentColor" />
                                                                </svg>
                                                            </div>
                                                            <div class="video-details">
                                                                <h4><?php echo esc_html($video['title']); ?></h4>
                                                                <div class="video-meta">
                                                                    <?php if (!empty($video['duration'])): ?>
                                                                        <span
                                                                            class="video-duration"><?php echo esc_html($video['duration']); ?></span>
                                                                    <?php endif; ?>
                                                                    <span
                                                                        class="video-access <?php echo $video['access'] === 'free' ? 'free' : 'paid'; ?>">
                                                                        <?php echo $video['access'] === 'free' ? 'رایگان' : 'پولی'; ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php if ($video['access'] === 'free'): ?>
                                                            <a href="<?php echo esc_url($video['url']); ?>" class="watch-video-btn"
                                                                target="_blank">
                                                                مشاهده
                                                            </a>
                                                        <?php else: ?>
                                                            <button class="watch-video-btn locked" disabled>
                                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"
                                                                        stroke="currentColor" stroke-width="2" />
                                                                    <path
                                                                        d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
                                                                        stroke="currentColor" stroke-width="2" />
                                                                </svg>
                                                                قفل شده
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Downloads Tab -->
                                <?php if ($download_files && !empty($download_files)): ?>
                                    <div class="tab-content" id="downloads">
                                        <div class="downloads-section">
                                            <h3>فایل‌های دانلودی</h3>
                                            <div class="downloads-list">
                                                <?php foreach ($download_files as $file): ?>
                                                    <div class="download-item">
                                                        <div class="file-info">
                                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
                                                                    stroke="currentColor" stroke-width="2" />
                                                                <polyline points="14,2 14,8 20,8" stroke="currentColor"
                                                                    stroke-width="2" />
                                                                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor"
                                                                    stroke-width="2" />
                                                                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor"
                                                                    stroke-width="2" />
                                                                <polyline points="10,9 9,9 8,9" stroke="currentColor"
                                                                    stroke-width="2" />
                                                            </svg>
                                                            <span class="file-title"><?php echo esc_html($file['title']); ?></span>
                                                        </div>
                                                        <a href="<?php echo esc_url($file['url']); ?>" class="download-btn" download>
                                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15"
                                                                    stroke="currentColor" stroke-width="2" />
                                                                <polyline points="7,10 12,15 17,10" stroke="currentColor"
                                                                    stroke-width="2" />
                                                                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor"
                                                                    stroke-width="2" />
                                                            </svg>
                                                            دانلود
                                                        </a>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Additional Info Tab -->
                                <div class="tab-content" id="info">
                                    <div class="additional-info-section">
                                        <div class="ex-row">
                                            <!-- Prerequisites -->
                                            <?php if ($prerequisites): ?>
                                                <div class="ex-col-lg-6 ex-col-12">
                                                    <div class="info-block">
                                                        <h3>
                                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M9 11H15M9 15H15M17 21L20 18L17 15M3 5C3 3.89543 3.89543 3 5 3H19C20.1046 3 21 3.89543 21 5V15C21 16.1046 20.1046 17 19 17H5C3.89543 17 3 16.1046 3 15V5Z"
                                                                    stroke="currentColor" stroke-width="2" />
                                                            </svg>
                                                            پیش‌نیازها
                                                        </h3>
                                                        <p><?php echo nl2br(esc_html($prerequisites)); ?></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Target Audience -->
                                            <?php if ($target_audience): ?>
                                                <div class="ex-col-lg-6 ex-col-12">
                                                    <div class="info-block">
                                                        <h3>
                                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M17 21V19C17 17.9391 16.5786 16.9217 15.8284 16.1716C15.0783 15.4214 14.0609 15 13 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21"
                                                                    stroke="currentColor" stroke-width="2" />
                                                                <circle cx="9" cy="7" r="4" stroke="currentColor"
                                                                    stroke-width="2" />
                                                                <path
                                                                    d="M23 21V19C23 18.1645 22.7155 17.3541 22.2094 16.7006C21.7033 16.047 20.9999 15.5866 20.2 15.3954"
                                                                    stroke="currentColor" stroke-width="2" />
                                                                <path
                                                                    d="M16 3.13C16.8003 3.32127 17.5037 3.78167 18.0098 4.43524C18.5159 5.08882 18.8004 5.89925 18.8004 6.735C18.8004 7.57075 18.5159 8.38118 18.0098 9.03476C17.5037 9.68833 16.8003 10.1487 16 10.34"
                                                                    stroke="currentColor" stroke-width="2" />
                                                            </svg>
                                                            مخاطب هدف
                                                        </h3>
                                                        <p><?php echo nl2br(esc_html($target_audience)); ?></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>

                                            <!-- Learning Outcomes -->
                                            <?php if ($learning_outcomes): ?>
                                                <div class="ex-col-12">
                                                    <div class="info-block">
                                                        <h3>
                                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                                                <path
                                                                    d="M22 11.08V12C21.9988 14.1564 21.3005 16.2547 20.0093 17.9818C18.7182 19.7088 16.9033 20.9725 14.8354 21.5839C12.7674 22.1953 10.5573 22.1219 8.53447 21.3746C6.51168 20.6273 4.78465 19.2461 3.61096 17.4371C2.43727 15.628 1.87979 13.4905 2.02168 11.3363C2.16356 9.18211 2.99721 7.13634 4.39828 5.49697C5.79935 3.85761 7.69279 2.71566 9.79619 2.24013C11.8996 1.7646 14.1003 1.98232 16.07 2.86"
                                                                    stroke="currentColor" stroke-width="2" />
                                                                <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor"
                                                                    stroke-width="2" />
                                                            </svg>
                                                            اهداف یادگیری
                                                        </h3>
                                                        <p><?php echo nl2br(esc_html($learning_outcomes)); ?></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Course Tags -->
            <?php if ($course_tags && !is_wp_error($course_tags)): ?>
                <section class="course-tags-section">
                    <div class="ex-row">
                        <div class="ex-col-12">
                            <div class="course-tags">
                                <h3>برچسب‌ها:</h3>
                                <div class="tags-list">
                                    <?php foreach ($course_tags as $tag): ?>
                                        <a href="<?php echo get_term_link($tag); ?>" class="tag-link">
                                            #<?php echo esc_html($tag->name); ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            <?php endif; ?>

        <?php endwhile; endif; ?>
    </div>
</main>

<!-- JavaScript for Tabs Functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Tab functionality
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const targetTab = this.getAttribute('data-tab');

                // Remove active class from all tabs and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });

        // Enrollment button functionality
        const enrollBtn = document.querySelector('.btn-enroll.btn-active');
        if (enrollBtn) {
            enrollBtn.addEventListener('click', function () {
                // Add your enrollment logic here
                alert('ثبت نام در دوره - این قسمت نیاز به پیاده‌سازی سیستم ثبت نام دارد');
            });
        }

        // Video lesson access control
        const lockedVideos = document.querySelectorAll('.watch-video-btn.locked');
        lockedVideos.forEach(btn => {
            btn.addEventListener('click', function () {
                alert('برای دسترسی به این ویدیو ابتدا در دوره ثبت نام کنید');
            });
        });
    });
</script>

<style>
    /* Course Single Page Styles */
    .course-single-page {
        padding: 40px 0;
        background-color: #FAF8F0;
        min-height: 100vh;
    }

    /* Course Header */
    .course-header {
        margin-bottom: 40px;
    }

    .course-breadcrumb {
        margin-bottom: 20px;
        font-size: 14px;
        color: var(--neutral600);
    }

    .course-breadcrumb a {
        color: var(--primary400);
        text-decoration: none;
    }

    .course-breadcrumb a:hover {
        text-decoration: underline;
    }

    .course-breadcrumb .separator {
        margin: 0 8px;
        color: var(--neutral400);
    }

    .course-breadcrumb .current {
        color: var(--neutral800);
        font-weight: 500;
    }

    .course-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--neutral900);
        margin-bottom: 20px;
        line-height: 1.2;
    }

    .course-meta-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--neutral700);
        font-size: 14px;
    }

    .meta-item svg {
        color: var(--primary400);
    }

    .course-categories {
        margin-bottom: 20px;
    }

    .category-tag {
        display: inline-block;
        background: var(--primary100);
        color: var(--primary700);
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-left: 8px;
        margin-bottom: 8px;
    }

    .course-description {
        font-size: 16px;
        line-height: 1.6;
        color: var(--neutral700);
    }

    /* Course Sidebar */
    .course-sidebar {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        position: sticky;
        top: 20px;
    }

    .course-preview-video,
    .course-thumbnail {
        margin-bottom: 24px;
    }

    .video-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%;
        /* 16:9 aspect ratio */
        border-radius: 8px;
        overflow: hidden;
    }

    .video-container iframe,
    .video-container video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }

    .course-image {
        width: 100%;
        height: auto;
        border-radius: 8px;
    }

    .no-image-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        background: var(--neutral100);
        border-radius: 8px;
        color: var(--neutral500);
    }

    /* Course Pricing */
    .course-pricing {
        margin-bottom: 24px;
    }

    .price-container {
        margin-bottom: 16px;
    }

    .price-free {
        font-size: 24px;
        font-weight: 700;
        color: var(--success400);
    }

    .price-sale {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .original-price {
        font-size: 16px;
        color: var(--neutral500);
        text-decoration: line-through;
    }

    .sale-price {
        font-size: 24px;
        font-weight: 700;
        color: var(--primary600);
    }

    .regular-price {
        font-size: 24px;
        font-weight: 700;
        color: var(--neutral800);
    }

    .discount-badge {
        background: var(--primary400);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        align-self: flex-start;
    }

    /* Enrollment Button */
    .btn-enroll {
        width: 100%;
        padding: 16px 24px;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .btn-enroll.btn-active {
        background: var(--primary400);
        color: white;
    }

    .btn-enroll.btn-active:hover {
        background: var(--primary500);
        transform: translateY(-2px);
    }

    .btn-enroll.btn-upcoming {
        background: var(--neutral300);
        color: var(--neutral600);
        cursor: not-allowed;
    }

    .btn-enroll.btn-completed {
        background: var(--success200);
        color: var(--success500);
        cursor: not-allowed;
    }

    /* Course Features */
    .course-features {
        margin-bottom: 24px;
    }

    .course-features h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 16px;
    }

    .features-list {
        list-style: none;
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        color: var(--neutral700);
    }

    .feature-item svg {
        color: var(--success400);
        flex-shrink: 0;
    }

    /* Certificate */
    .course-certificate {
        border: 2px solid var(--primary200);
        border-radius: 8px;
        padding: 16px;
        background: var(--primary50);
    }

    .certificate-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .certificate-info svg {
        color: var(--primary500);
        flex-shrink: 0;
    }

    .certificate-info h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 4px;
    }

    .certificate-info p {
        font-size: 14px;
        color: var(--neutral600);
        margin: 0;
    }

    /* Tabs */
    .course-content-tabs {
        margin-bottom: 40px;
    }

    .tabs-container {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .tabs-nav {
        display: flex;
        background: var(--neutral100);
        border-bottom: 1px solid var(--neutral200);
        overflow-x: auto;
    }

    .tab-btn {
        padding: 16px 24px;
        border: none;
        background: transparent;
        color: var(--neutral600);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        white-space: nowrap;
        transition: all 0.3s ease;
        border-bottom: 3px solid transparent;
    }

    .tab-btn:hover {
        background: var(--neutral200);
        color: var(--neutral800);
    }

    .tab-btn.active {
        background: white;
        color: var(--primary600);
        border-bottom-color: var(--primary400);
    }

    .tabs-content {
        padding: 32px;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .course-full-description {
        font-size: 16px;
        line-height: 1.8;
        color: var(--neutral700);
    }

    .course-full-description h1,
    .course-full-description h2,
    .course-full-description h3,
    .course-full-description h4,
    .course-full-description h5,
    .course-full-description h6 {
        color: var(--neutral800);
        margin-top: 24px;
        margin-bottom: 16px;
    }

    .course-full-description p {
        margin-bottom: 16px;
    }

    .course-full-description ul,
    .course-full-description ol {
        margin-bottom: 16px;
        padding-right: 20px;
    }

    .course-full-description li {
        margin-bottom: 8px;
    }

    /* Curriculum */
    .curriculum-section h3 {
        font-size: 24px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 24px;
    }

    .curriculum-item {
        border: 1px solid var(--neutral200);
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
    }

    .lesson-header {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        padding: 20px;
        background: var(--neutral50);
    }

    .lesson-number {
        width: 32px;
        height: 32px;
        background: var(--primary400);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        flex-shrink: 0;
    }

    .lesson-info {
        flex: 1;
    }

    .lesson-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 8px;
    }

    .lesson-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
    }

    .lesson-duration {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: var(--neutral600);
    }

    .lesson-duration svg {
        color: var(--primary400);
    }

    .lesson-free {
        background: var(--success100);
        color: var(--success600);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .lesson-quiz {
        background: var(--secondary100);
        color: var(--secondary600);
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .lesson-description {
        padding: 0 20px 20px;
        color: var(--neutral600);
        line-height: 1.6;
    }

    /* Video Lessons */
    .video-lessons-section h3 {
        font-size: 24px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 24px;
    }

    .video-lesson-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border: 1px solid var(--neutral200);
        border-radius: 8px;
        margin-bottom: 12px;
        background: white;
    }

    .video-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .video-thumbnail {
        width: 40px;
        height: 40px;
        background: var(--primary100);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary500);
    }

    .video-details h4 {
        font-size: 16px;
        font-weight: 500;
        color: var(--neutral800);
        margin-bottom: 4px;
    }

    .video-meta {
        display: flex;
        gap: 12px;
        font-size: 14px;
    }

    .video-duration {
        color: var(--neutral600);
    }

    .video-access.free {
        color: var(--success500);
        font-weight: 500;
    }

    .video-access.paid {
        color: var(--primary500);
        font-weight: 500;
    }

    .watch-video-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
    }

    .watch-video-btn:not(.locked) {
        background: var(--primary400);
        color: white;
    }

    .watch-video-btn:not(.locked):hover {
        background: var(--primary500);
    }

    .watch-video-btn.locked {
        background: var(--neutral200);
        color: var(--neutral500);
        cursor: not-allowed;
    }

    /* Downloads */
    .downloads-section h3 {
        font-size: 24px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 24px;
    }

    .download-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border: 1px solid var(--neutral200);
        border-radius: 8px;
        margin-bottom: 12px;
        background: white;
    }

    .file-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .file-info svg {
        color: var(--primary400);
    }

    .file-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--neutral800);
    }

    .download-btn {
        padding: 8px 16px;
        background: var(--success400);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
    }

    .download-btn:hover {
        background: var(--success500);
    }

    /* Additional Info */
    .additional-info-section {
        margin-top: 20px;
    }

    .info-block {
        background: var(--neutral50);
        padding: 24px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .info-block h3 {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 18px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 16px;
    }

    .info-block h3 svg {
        color: var(--primary400);
    }

    .info-block p {
        font-size: 16px;
        line-height: 1.6;
        color: var(--neutral700);
        margin: 0;
    }

    /* Course Tags */
    .course-tags-section {
        margin-bottom: 40px;
    }

    .course-tags h3 {
        font-size: 18px;
        font-weight: 600;
        color: var(--neutral800);
        margin-bottom: 16px;
    }

    .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .tag-link {
        display: inline-block;
        background: var(--secondary100);
        color: var(--secondary600);
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .tag-link:hover {
        background: var(--secondary200);
        color: var(--secondary700);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .course-title {
            font-size: 2rem;
        }

        .course-meta-info {
            flex-direction: column;
            gap: 12px;
        }

        .tabs-nav {
            flex-direction: column;
        }

        .tab-btn {
            text-align: right;
            border-bottom: 1px solid var(--neutral200);
            border-left: 3px solid transparent;
        }

        .tab-btn.active {
            border-bottom-color: var(--neutral200);
            border-left-color: var(--primary400);
        }

        .tabs-content {
            padding: 20px;
        }

        .video-lesson-item,
        .download-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }

        .watch-video-btn,
        .download-btn {
            align-self: stretch;
            justify-content: center;
        }
    }
</style>

<?php get_footer(); ?>