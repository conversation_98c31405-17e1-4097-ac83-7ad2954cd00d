jQuery(document).ready(function ($) {
  $(window).scrollTop(0);

  //to top button
  $("#scroll-top").click(function () {
    $("html, body").animate({ scrollTop: 0 }, "slow");
    return false;
  });

  //slider
  var blogSlider = new Splide(".ex-blog-slider", {
    type: "loop",
    perPage: 3,
    perMove: 1,
    focus: 0,
    omitEnd: true,
    direction: "rtl",
    arrows: false,
    pagination: false,
    breakpoints: {
      767: {
        perPage: 1,
        arrows: false,
        pagination: true,
      },
      1024: {
        perPage: 2,
        arrows: false,
        pagination: true,
      },
    },
    gap: "30px",
  });
  blogSlider.mount();
  document.getElementById("arrow-prev").addEventListener("click", function () {
    blogSlider.go("<");
  });

  document.getElementById("arrow-next").addEventListener("click", function () {
    blogSlider.go(">");
  });

  var portfolioSlider = new Splide(".ex-portfolio-slider", {
    type: "loop",
    perPage: 3,
    perMove: 1,
    focus: 0,
    omitEnd: true,
    direction: "rtl",
    arrows: false,
    pagination: false,
    breakpoints: {
      767: {
        perPage: 1,
        arrows: false,
        pagination: true,
      },
      1024: {
        perPage: 2,
        arrows: false,
        pagination: true,
      },
    },
    gap: "30px",
  });

  portfolioSlider.mount();

  document
    .getElementById("arrow-prev-portfolio")
    .addEventListener("click", function () {
      portfolioSlider.go("<");
    });

  document
    .getElementById("arrow-next-portfolio")
    .addEventListener("click", function () {
      portfolioSlider.go(">");
    });


  var coursesSlider = new Splide(".ex-courses-slider", {
    type: "loop",
    perPage: 3,
    perMove: 1,
    focus: 0,
    omitEnd: true,
    direction: "rtl",
    arrows: false,
    pagination: false,
    breakpoints: {
      767: {
        perPage: 1,
        arrows: false,
        pagination: true,
      },
      1024: {
        perPage: 2,
        arrows: false,
        pagination: true,
      },
    },
    gap: "30px",
  });
  
  coursesSlider.mount();

  document
    .getElementById("arrow-prev-courses")
    .addEventListener("click", function () {
      coursesSlider.go("<");
    });

  document
    .getElementById("arrow-next-courses")
    .addEventListener("click", function () {
      coursesSlider.go(">");
    });

  var productSlider = new Splide(".ex-products-slider", {
    type: "loop",
    perPage: 4,
    perMove: 1,
    focus: 0,
    omitEnd: true,
    direction: "rtl",
    arrows: false,
    pagination: false,
    breakpoints: {
      767: {
        perPage: 1.3,
        arrows: false,
        pagination: true,
      },
      1024: {
        perPage: 2,
        arrows: false,
        pagination: true,
      },
    },
    gap: "30px",
  });
  
  productSlider.mount();

  document
    .getElementById("arrow-prev-products")
    .addEventListener("click", function () {
      productSlider.go("<");
    });

  document
    .getElementById("arrow-next-products")
    .addEventListener("click", function () {
      productSlider.go(">");
    });




});
