.ex_pyramid_and_lorem {
    width: 100%;
    display: flex;
    flex-direction: row;
}

.ex_pyramid {
    position: absolute;
    top: 0;
    left: calc(-1 * ((64vw - 100%) / 2));
    width: 57vw;
    height: -webkit-fill-available;
    /* overflow-x: hidden; */
    z-index: -1;
}

.ex_pyramid_wrapper {
    position: relative;
    width: 57%;
    aspect-ratio: 16 / 9;
}

.ex_pyramid_wrapper::after {
    content: "";
    display: block;
    height: 510px;
}

.ex_pyramid_inner {
    position: relative;
    width: 100%;
    height: 0;
    padding-top: 100%;
    transform: scale(1);
    transform-origin: top left;
}

.ex_lorem_right {
    display: flex;
    flex-direction: column;
    width: 50%;
    align-items: start;
    gap: 30px;
    margin-top: 100px;
}

.ex_pyramid_and_lorem_header {
    display: none;
}

.ex_lorem_right h1.ex_pyramid_and_lorem_header {
    color: var(--light-background, #FAF8F0);
    font-weight: 700;
    line-height: 70px;
    display: flex;
}

.ex_lorem_right p {
    color: var(--light-background, #FAF8F0);
    font-weight: 400;
    line-height: 32px;
}

.ex_pyramid svg {
    position: absolute;
    height: auto;
    transform-origin: top left;
    transform: translate(-50%, 0%) scale(0.81);
    z-index: 4;
    transition: all 6s ease-in-out;
}

.ex_pyramid img {
    position: absolute;
    z-index: 2;
    left: 67%;
    top: 10%;
    width: 100%;
    height: auto;
    transform: translate(-50%, 0%) scale(0.81);
    transform-origin: top left;
    transition: all 6s ease-in-out;
}

.ex_pyramid img.active_one {
    transform: translate(-50%, -1.5%) scale(0.81);
}

.ex_pyramid img.active_two {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid img.active_four {
    transform: translate(-50%, 0) scale(0.81);
}

.ex_pyramid img.active_five {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid svg.ex_static_objects {
    left: 51%;
    top: 9%;
    z-index: 4;
    width: 100%;
    height: auto;
    transform: translate(-50%, 0%) scale(0.81);
}

.ex_pyramid svg.ex_static_objects.active_one {
    transform: translate(-50%, -1.5%) scale(0.81);
}

.ex_pyramid svg.ex_static_objects.active_two {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid svg.ex_static_objects.active_four {
    transform: translate(-50%, 0) scale(0.81);
}

.ex_pyramid svg.ex_static_objects.active_five {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_two_pyramid_up {
    top: 34%;
    left: 60%;
    width: 3.3vw;
    z-index: 3;
}

.ex_pyramid .svg_two_pyramid_up.active_one {
    transform: translate(-50%, -1.5%) scale(0.81);
}

.ex_pyramid .svg_two_pyramid_up.active_two {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_two_pyramid_up.active_four {
    transform: translate(-50%, 0) scale(0.81);
}

.ex_pyramid .svg_two_pyramid_up.active_five {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_three_pyramid_up {
    top: 71%;
    left: 18%;
    width: 4vw;
    z-index: 3;
}

.ex_pyramid .svg_three_pyramid_up.active_one {
    transform: translate(-50%, -1.5%) scale(0.81);
}

.ex_pyramid .svg_three_pyramid_up.active_two {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_three_pyramid_up.active_four {
    transform: translate(-50%, 0) scale(0.81);
}

.ex_pyramid .svg_three_pyramid_up.active_five {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_four_pyramid_up {
    top: 34%;
    left: 43%;
    width: 3vw;
    z-index: 3;
}

.ex_pyramid .svg_four_pyramid_up.active_one {
    transform: translate(-50%, -1.5%) scale(0.81);
}

.ex_pyramid .svg_four_pyramid_up.active_two {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_four_pyramid_up.active_four {
    transform: translate(-50%, 0) scale(0.81);
}

.ex_pyramid .svg_four_pyramid_up.active_five {
    transform: translate(-50%, 1.5%) scale(0.81);
}

.ex_pyramid .svg_main_pyramid_center {
    left: 69.2%;
    top: 5%;
    width: 33.5vw;
}

.ex_pyramid .svg_main_pyramid_center.active_one {
    transform: translate(-51%, 7.5%) scale(0.68);
}

.ex_pyramid .svg_main_pyramid_center.active_two {
    transform: translate(-51%, 10.5%) scale(0.68);
}

.ex_pyramid .svg_main_pyramid_center.active_four {
    transform: translate(-48%, 9%) scale(0.61);
}

.ex_pyramid .svg_main_pyramid_center.active_five {
    transform: translate(-48%, 10.5%) scale(0.61);
}

.ex_pyramid .svg_1_pyramid_down {
    top: 58%;
    left: 18%;
    width: 3.5vw;
    z-index: 3;
}

.ex_pyramid .svg_1_pyramid_down.active_one {
    transform: rotate(-44deg) translate(-69%, 5%) scale(0.8);
}

.ex_pyramid .svg_1_pyramid_down.active_four {
    transform: translate(-69%, 16%) scale(0.75);
}

.ex_pyramid .svg_2_pyramid_down {
    top: 62%;
    left: 24%;
    width: 3vw;
    z-index: 3;
}

.ex_pyramid .svg_2_pyramid_down.active_one {
    transform: rotate(28deg) translate(138%, -126%) scale(0.95);
}

.ex_pyramid .svg_2_pyramid_down.active_four {
    transform: translate(-70%, -10%) scale(0.79);
}

.ex_pyramid .svg_3_pyramid_down {
    top: 60%;
    left: 66.2%;
    width: 4vw;
    z-index: 3;
}

.ex_pyramid .svg_3_pyramid_down.active_one {
    transform: rotate(-44deg) translate(-14%, 37%) scale(0.67);
}

.ex_pyramid .svg_3_pyramid_down.active_four {
    transform: translate(-89%, -14%) scale(0.78);
}

.ex_pyramid .svg_4_pyramid_down {
    top: 57%;
    left: 77%;
    width: 3vw;
    z-index: 3;
}

.ex_pyramid .svg_4_pyramid_down.active_one {
    transform: rotate(-44deg) translate(-6%, -22%) scale(0.78);
}

.ex_pyramid .svg_4_pyramid_down.active_four {
    transform: rotate(18deg) translate(-43%, 27%) scale(0.78);
}

.ex_pyramid .svg_5_pyramid_down {
    top: 58.5%;
    left: 80.5%;
    width: 2.5vw;
    z-index: 3;
}

.ex_pyramid .svg_5_pyramid_down.active_one {
    transform: translate(-47%, 30%) scale(0.41);
}

.ex_pyramid .svg_5_pyramid_down.active_four {
    transform: rotate(2deg) translate(-60%, 71%) scale(0.65);
}

.ex_pyramid .svg_one_pyramid_up {
    top: 33%;
    left: 69%;
    width: 6vw;
    z-index: 3;
}

.ex_pyramid .svg_one_pyramid_up.active_one {
    transform: rotate(-44deg) translate(-31%, -3%) scale(0.81);
}

.ex_pyramid .svg_one_pyramid_up.active_four {
    transform: rotate(14deg) translate(-26%, -4%) scale(0.81);
}

.ex_pyramid .svg_five_pyramid_up {
    top: 28.5%;
    left: 31%;
    width: 10vw;
    z-index: 3;
}

.ex_pyramid .svg_five_pyramid_up.active_one {
    transform: rotate(-15deg) translate(-20%, -3%) scale(0.81);
}

.ex_pyramid .svg_five_pyramid_up.active_four {
    transform: rotate(13deg) translate(-34%, 18%) scale(0.81);
}

.ex_pyramid .svg_six_pyramid_up {
    top: 20.5%;
    left: 33%;
    width: 5vw;
    z-index: 3;
}

.ex_pyramid .svg_six_pyramid_up.active_one {
    transform: rotate(-20deg) translate(-3%, -18%) scale(0.71);
}

.ex_pyramid .svg_six_pyramid_up.active_four {
    transform: rotate(-7deg) translate(-44%, 13%) scale(0.7);
}

.ex_pyramid .svg_seven_pyramid_up {
    top: 9%;
    left: 23.6%;
    width: 7vw;
    z-index: 3;
}

.ex_pyramid .svg_seven_pyramid_up.active_one {
    transform: rotate(26deg) translate(11%, -14%) scale(0.9);
}

.ex_pyramid .svg_seven_pyramid_up.active_four {
    transform: rotate(1deg) translate(-39%, 13%) scale(0.89);
}

.ex_pyramid .svg_eight_pyramid_up {
    top: 3.5%;
    left: 18%;
    width: 2.7vw;
    z-index: 3;
}

.ex_pyramid .svg_eight_pyramid_up.active_one {
    transform: rotate(-34deg) translate(-80%, 72%) scale(0.81);
}

.ex_pyramid .svg_eight_pyramid_up.active_four {
    transform: rotate(45deg) translate(36%, -27%) scale(0.81);
}

.ex_pyramid .svg_one_pyramid_center {
    top: 26%;
    left: 74.6%;
    width: 3.5vw;
    z-index: 3;
}

.ex_pyramid .svg_one_pyramid_center.active_one {
    transform: rotate(-68deg) translate(-12%, 31%) scale(1.2);
}

.ex_pyramid .svg_one_pyramid_center.active_four {
    transform: rotate(-9deg) translate(-60%, 2%) scale(0.9);
}

.ex_pyramid .svg_two_pyramid_center {
    top: 21.4%;
    left: 71.7%;
    width: 4.8vw;
    z-index: 1;
}

.ex_pyramid .svg_two_pyramid_center.active_one {
    transform: translate(-92%, 0%) scale(0.81);
}

.ex_pyramid .svg_two_pyramid_center.active_four {
    transform: rotate(4deg) translate(-21%, -32%) scale(0.86);
}

.ex_pyramid .svg_three_pyramid_center {
    top: 15.5%;
    left: 77.6%;
    width: 3vw;
    z-index: 3;
}

.ex_pyramid .svg_three_pyramid_center.active_one {
    transform: rotate(-52deg) translate(9%, -62%) scale(0.81);
}

.ex_pyramid .svg_three_pyramid_center.active_four {
    transform: rotate(-18deg) translate(-311%, -78%) scale(1.6);
}

.ex_pyramid .svg_four_pyramid_center {
    top: 13.2%;
    left: 67.4%;
    width: 5vw;
    z-index: 1;
}

.ex_pyramid .svg_four_pyramid_center.active_one {
    transform: rotate(-18deg) translate(-107%, 0) scale(0.81);
}

.ex_pyramid .svg_four_pyramid_center.active_four {
    transform: rotate(18deg) translate(-51%, -33%) scale(0.9);
}

.ex_pyramid .svg_one_top_pyramid_center {
    top: 3.5%;
    left: 56.6%;
    width: 1.2vw;
    z-index: 3;
}

.ex_pyramid .svg_one_top_pyramid_center.active_one {
    transform: rotate(-93deg) translate(-499%, -394%) scale(0.9);
}

.ex_pyramid .svg_one_top_pyramid_center.active_four {
    transform: rotate(-171deg) translate(-200%, -396%) scale(0.96);
}

.ex_pyramid .svg_two_top_pyramid_center {
    top: 8.1%;
    left: 46.6%;
    width: 1.5vw;
    z-index: 3;
}

.ex_pyramid .svg_two_top_pyramid_center.active_one {
    transform: rotate(-90deg) translate(-263%, -90%) scale(0.81);
}

.ex_pyramid .svg_two_top_pyramid_center.active_four {
    transform: rotate(-51deg) translate(-44%, -47%) scale(0.9);
}

.ex_pyramid .svg_three_top_pyramid_center {
    top: 6.5%;
    left: 40.6%;
    width: 1.7vw;
    z-index: 3;
}

.ex_pyramid .svg_three_top_pyramid_center.active_one {
    transform: translate(-50%, -100%) scale(0.81);
}

.ex_pyramid .svg_three_top_pyramid_center.active_four {
    transform: rotate(-4deg) translate(-24%, -8%) scale(0.81);
}

.ex_pyramid .svg_four_top_pyramid_center {
    top: 10.5%;
    left: 36.5%;
    width: 2.5vw;
    z-index: 3;
}

.ex_pyramid .svg_four_top_pyramid_center.active_one {
    transform: rotate(-61deg) translate(-15%, 32%) scale(0.81);
}

.ex_pyramid .svg_four_top_pyramid_center.active_four {
    transform: rotate(-28deg) translate(-26%, 186%) scale(1.2);
}

.ex_pyramid .svg_five_top_pyramid_center {
    top: 9.5%;
    left: 31.6%;
    width: 2.5vw;
    z-index: 3;
}

.ex_pyramid .svg_four_top_pyramid_center.active_one {
    transform: rotate(-63deg) translate(-23%, -2%) scale(0.75);
}

.ex_pyramid .svg_four_top_pyramid_center.active_four {
    transform: rotate(-7deg) translate(-3%, 14%) scale(0.87);
}

@media (max-width: 1199px) {
    .ex_lorem_right {
        margin-top: 85px;
    }
}

@media (max-width: 991px) {
    .ex_lorem_right {
        margin-top: 65px;
    }
}

@media (max-width: 767px) {
    .ex_pyramid_and_lorem_header {
        color: var(--light-background, #FAF8F0);
        font-weight: 700;
        font-size: 44px;
        line-height: 70px;
        display: flex;
    }

    .ex_lorem_right h1.ex_pyramid_and_lorem_header {
        display: none;
    }

    .ex_pyramid_and_lorem {
        flex-direction: column-reverse;
    }

    .ex_lorem_right {
        width: 100%;
        margin-top: 35px;
        align-items: center;
    }

    .ex_pyramid_wrapper {
        width: 100%;
    }

    .ex_pyramid {
        width: 101vw;
        left: calc(-1 * ((100vw - 100%) / 2));
    }

    .ex_pyramid .svg_main_pyramid_center {
        width: 61.5vw;
    }
}

@media (max-width: 575px) {
    .ex_pyramid_wrapper::after {
        height: 400px;
    }

    .ex_lorem_right {
        margin-top: 0;
    }
}

@media (max-width: 450px) {
    .ex_pyramid_wrapper::after {
        height: 310px;
    }
}