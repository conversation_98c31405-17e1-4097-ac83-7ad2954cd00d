/* footer */

.socialmedia-footer {
  display: flex;
  gap: 30px;
  border-bottom: 1px solid #f0c434;
  border-top: 1px solid #f0c434;
  padding-top: 12px;
  align-items: center;
  width: fit-content;
}

.copy-right {
  border-top: 1px solid;
  border-bottom: 1px solid;
  text-align: center;
  padding: 6px 0;
}
.border-gradient-copy-right {
  border-image: linear-gradient(to right, #fbf5b7, #b38728) 1;
}

.ex-col-footer {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.ex-col-footer ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.ex-col-footer h3 {
  font-size: 16px;
  border-bottom: 1px solid #f0c434;
  width: fit-content;
}
.ex-col-footer a,
p {
  font-size: 15px;
}

.ex-col-footer .ex-contact-footer {
  display: flex;
  flex-direction: column;
}
.ex-contact-footer li {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px !important;
}

.ex-col-footer-contact h3 {
  font-size: 16px;
  border-bottom: 1px solid #f0c434;
  width: fit-content;
  margin-top: 20px;
}
.ex-col-footer-contact {
  gap: 50px;
  display: flex;
  flex-direction: column;
}
.ex-communication {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.main-footer {
  margin-bottom: 40px;
}
.logo-box-footer {
  background: #1a1918;
  border-radius: 0 0 30px 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 194px;
  border: 1px solid var(--secondary500);
  border-top: none;
  padding-block: 10px;
  position: relative;
  margin-inline: auto;
  margin-bottom: 50px;
  top: -1px;
}

#ex-footer {
  background: rgba(0, 0, 0, 0.6);
  border-top: 1px solid var(--secondary500);
  padding-bottom: 30px;
}

.ex-rhombus-footer {
  position: absolute;
  width: 798px;
  height: 768px;
  max-width: 100%;
  max-height: 100%;
  bottom: 60px;
  left: 0;
  z-index: -10;
  animation: moveLoop 6s ease-in-out infinite;
}

@keyframes moveLoop {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(900px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes moveLoop1 {
  0%,
  50% {
    transform: rotate(0deg) translateY(0);
  }
  50% {
    transform: rotate(90deg) translateY(20px);
  }
}

@keyframes moveLoop2 {
  0%,
  50% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-30px) scale(1.1);
  }
}

@keyframes moveLoop3 {
  0%,
  50% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-40px);
  }
}

@keyframes moveLoop4 {
  0%,
  50% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
}

@keyframes moveLoop5 {
  0%,
  50% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(10px, -10px);
  }
  50% {
    transform: translate(-10px, 10px);
  }
  75% {
    transform: translate(10px, 5px);
  }
}

@keyframes moveLoop6 {
  0%,
  50% {
    transform: rotate(0deg) translateX(0);
  }
  50% {
    transform: rotate(-90deg) translateX(30px);
  }
}

.rhombus1 {
  position: absolute;
  left: 0;
  top: 0;
  animation: moveLoop1 3s ease-in-out infinite;
}
.rhombus2 {
  position: absolute;
  left: 40px;
  top: 0;
  animation: moveLoop2 3s ease-in-out infinite;
}
.rhombus3 {
  position: absolute;
  left: 470px;
  top: 150px;
  animation: moveLoop3 3s ease-in-out infinite;
}
.rhombus4 {
  position: absolute;
  right: 384px;
  bottom: 220px;
  animation: moveLoop4 3s ease-in-out infinite;
}
.rhombus5 {
  position: absolute;
  left: 205px;
  bottom: 122px;
  animation: moveLoop5 3s ease-in-out infinite;
}
.rhombus6 {
  position: absolute;
  right: 321px;
  bottom: 0;
  animation: moveLoop6 3s ease-in-out infinite;
}

.ex-row.main-footer .ex-col-lg-3.ex-col-sm-6.ex-col-12 {
  z-index: 1;
}

.ex-scroll-top {
  border-bottom: 20px solid #f0c434;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: none;
  width: 0;
  height: 0;
}

#scroll-top {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 14px;
  width: fit-content;
  cursor: pointer;
  position: fixed;
  left: 20px;
  bottom: 20px;
  transition: 0.3s;
  border: 1px solid transparent;
}
#scroll-top:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

@media (min-width: 368px) and (max-width: 767px) {
  .ex-col-footer ul {
    gap: 20px;
  }
  .ex-col-footer {
    gap: 10px;
  }

  ul.ex-contact-footer {
    display: flex;
    flex-direction: column;
    gap: 18px;
  }
  .ex-container {
    padding-bottom: 20px;
  }

  .socialmedia-footer {
    padding-top: 6px;
    flex-direction: column-reverse;
    border: none;
    margin-top: 30px;
    gap: 10px;
  }
  .socialmedia-footer a svg {
    width: 30px;
    height: 30px;
  }
  .ex-col-footer-contact {
    gap: 40px;
    display: flex;
    flex-direction: row;
  }

  .logo-box-footer {
    top: -2px;
    width: 148px;
    height: 62px;
    min-height: 62px;
    padding-inline: 16px;
  }
  .ex-rhombus-footer {
    display: none;
  }

  .col-services-footer {
    width: 50%;
    margin-top: 30px;
  }
  .col-quick-footer {
    width: 50%;
    margin-top: 30px;
  }
  .col-contact-footer {
    margin-top: 30px;
  }

  #scroll-top {
    left: 20px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  
}

/* blog */

.ex-blog-card {
  padding: 20px 17px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  background: #040404;
  position: relative;
  z-index: 1;
}
.ex-blog-card:before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(to right, #fbf5b7, #b38728);
  border-radius: inherit;
  mask: conic-gradient(#000 0 0) content-box exclude, conic-gradient(#000 0);
}

.ex-blog-card-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 44px;
  padding: 6px 12px 6px 6px;
  background: #141413;
  border-radius: 50px;
}
.ex-blog-card-info ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 14px;
}
.ex-blog-card-info ul li {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  color: #faf8f0;
}

.ex-button {
  position: relative;
  display: flex;
  gap: 6px;
  align-items: center;
  padding: 8px 22px;
  color: #fff;
  font-weight: 400;
  background-color: #6ddbe8;
  text-decoration: none;
  overflow: hidden;
  border-radius: 50px;
  transition: color 0.3s ease;
  z-index: 1;
}

.ex-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: #f0c434;
  z-index: 3;
  transition: width 0.4s ease;
}

.ex-button:hover::before {
  width: 100%;
}

.ex-button:hover {
  color: #fff;
}
.ex-button:hover span {
  visibility: hidden;
}

.hover-icon-ex-button {
  display: none;
  height: 30px;
  width: 30px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 4;
  transform: translateY(-50%) translateX(0) rotate(0deg);
}

.ex-button:hover .hover-icon-ex-button {
  display: inline-block;
  animation: moveAndRotate 1s forwards;
}

@keyframes moveAndRotate {
  0% {
    left: 0;
    transform: translateY(-50%) translateX(0) rotate(0deg);
  }
  100% {
    left: 50%;
    transform: translateY(-50%) translateX(-50%) rotate(360deg);
  }
}

.ex-button span {
  color: #191918;
}

.ex-blog-card-info h4 {
  color: #faf8f0;
}

.ex-blog-front-title {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

.ex-blog-slider {
  display: flex;
}

.arrow-prev {
  border-bottom: 12px solid transparent;
  border-left: 16px solid #f0c434;
  border-right: none;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-prev {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 15px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 270px;
  top: 0;
}

#arrow-prev:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.arrow-next {
  border-bottom: 12px solid transparent;
  border-left: none;
  border-right: 16px solid #f0c434;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-next {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 18px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 210px;
  top: 0;
}
#arrow-next:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.ex-blog-front-title {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.ex-blog-front-title h2 {
  color: #faf8f0;
}

.ex-bt-blog-front:hover .icon-defult-ex-bt-blog-front {
  visibility: hidden;
}

.ex-blog-front-first {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.front-line {
  display: flex;
  justify-content: flex-end;
}

.splide__pagination .splide__pagination__page {
  background: #f0c434;
  height: 20px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}
.splide__pagination .splide__pagination__page.is-active {
  background: #6ddbe8;
  height: 27px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}

.ex-blog-front {
  position: relative;
}

.ex-blog-slider {
  margin-bottom: 70px;
}

@media (min-width: 368px) and (max-width: 767px) {
  #arrow-prev {
    display: none;
  }

  #arrow-next {
    display: none;
  }
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -56px;
  }

  .icon-defult-ex-bt-blog-front {
    width: 16px;
    height: 16px;
  }
  .ex-blog-front-title {
    margin-bottom: 30px;
  }
  .ex-blog-card-info ul {
    gap: 10px;
  }
  .ex-blog-card-info {
    gap: 66px;
  }
  .ex-blog-front-title {
    gap: 10px;
  }
  .ex-button {
    padding: 2px 20px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -60px;
  }
  #arrow-prev {
    display: none;
  }

  #arrow-next {
    display: none;
  }
  .ex-blog-card-info {
    gap: 46px;
  }
  .ex-blog-card-info ul {
    gap: 10px;
  }
}

/* portfolio */

.ex-portfolio-front-title {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

.ex-portfolio-front-title {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.ex-portfolio-front-title h2 {
  color: #faf8f0;
}

.ex-bt-portfolio-front:hover .icon-defult-ex-bt-portfolio-front {
  visibility: hidden;
}

.ex-portfolio-front-first {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.front-line {
  display: flex;
  justify-content: flex-end;
}

.ex-portfolio-front {
  position: relative;
  margin-bottom: 100px;
}

.ex-portfolio-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: visible;
  position: relative;
}
.ex-portfolio-card .ex-portfolio-img {
  transition: transform 0.3s ease;
  display: block;
  width: 100%;
}
.ex-portfolio-card:hover .ex-portfolio-img {
  transform: scale(1.1);
}
.ex-portfolio-gradient {
  background: linear-gradient(to top, #ff9bb6, #ff9bb500);
  position: absolute;
  height: 40px;
  padding: 25px;
  bottom: 0;
  z-index: 0;
  width: 370px;
  border-radius: 0 0 50px 50px;
  transition: 0.5s ease;
}
.ex-portfolio-gradient.tahamiclinic {
  background: linear-gradient(to top, #082288, #ff9bb500);
}
.ex-portfolio-gradient.tahamionline {
  background: linear-gradient(to top, #ff9bb6, #ff9bb500);
}
.ex-portfolio-gradient.hookah {
  background: linear-gradient(to top, #23a423, #ff9bb500);
}

.ex-portfolio-card:hover .ex-portfolio-gradient {
  height: 10px;
  padding: 8px;
}
.ex-portfolio-card svg {
  z-index: 1;
}
.ex-portfolio-img {
  z-index: -1;
}

.arrow-prev-portfolio {
  border-bottom: 12px solid transparent;
  border-left: 16px solid #f0c434;
  border-right: none;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-prev-portfolio {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 15px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 270px;
  top: 0;
}

#arrow-prev-portfolio:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.arrow-next-portfolio {
  border-bottom: 12px solid transparent;
  border-left: none;
  border-right: 16px solid #f0c434;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-next-portfolio {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 18px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 210px;
  top: 0;
}
#arrow-next-portfolio:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}
.ex-portfolio-slider {
  margin-top: 80px;
}

.ex-portfolio-card-hover-svg {
  position: absolute;
  bottom: -60px;
  left: 150px;
  top: 180px;
  z-index: 1;
  transition: 0.5s ease;
}
.ex-portfolio-card:hover .ex-portfolio-card-hover-svg {
  top: 140px;
}

.ex-portfolio-card .ex-portfolio-logo {
  position: absolute;
  left: 50%;
  bottom: 14px;
  transform: translateX(-50%) ;
  z-index: 1;
  transition: 0.4s ease-in-out;
}
.ex-portfolio-card:hover .ex-portfolio-logo {

  left: -14px;
  bottom: 80%;
  transform: translateX(0) ;
  opacity: 1;
}

@keyframes moveLogoPortfolio {
  0% {
    left: 0;
    transform: translateY(0) translateX(0);
  }
  100% {
    /* left: 50%; */
    transform: translateY(0%) translateX(0%);
  }
}

@media (min-width: 368px) and (max-width: 767px) {
  .icon-defult-ex-bt-portfolio-front {
    width: 16px;
    height: 16px;
  }
  .ex-portfolio-front-title {
    margin-bottom: 30px;
  }
  .ex-portfolio-front-title {
    gap: 10px;
  }
  #arrow-next-portfolio {
    display: none;
  }
  #arrow-prev-portfolio {
    display: none;
  }
  .ex-portfolio-gradient {
    width: 400px;
  }
}
@media (max-width: 400px) and (max-height: 900px) {
  .ex-button-products {
    padding: 9px 10px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  #arrow-next-portfolio {
    display: none;
  }
  #arrow-prev-portfolio {
    display: none;
  }
}

.ex-portfolio-slider .splide__track {
  padding-top: 10px;
}
/* courses */

.hover-icon-ex-button-card-courses {
  display: none;
  height: 30px;
  width: 30px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 4;
  transform: translateY(-50%) translateX(0) rotate(0deg);
}

.ex-button:hover .hover-icon-ex-button-card-courses {
  display: inline-block;
  animation: moveAndRotate 1s forwards;
}

@keyframes moveAndRotate {
  0% {
    left: 0;
    transform: translateY(-50%) translateX(0) rotate(0deg);
  }
  100% {
    left: 50%;
    transform: translateY(-50%) translateX(-50%) rotate(360deg);
  }
}

.ex-courses-card {
  padding: 20px 17px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  background: #040404;
  position: relative;
  z-index: 1;
}
.ex-courses-card:before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(to right, #fbf5b7, #b38728);
  border-radius: inherit;
  mask: conic-gradient(#000 0 0) content-box exclude, conic-gradient(#000 0);
}

.ex-courses-card-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 15px;
  padding: 6px 12px 6px 6px;
  background: #141413;
  border-radius: 50px;
}
.ex-courses-card-info ul {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24px;
}
.ex-courses-card-info ul li {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  font-size: 12px;
  color: #faf8f0;
}

.ex-courses-card-info h4 {
  color: #faf8f0;
}

.ex-courses-front-title {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

.ex-courses-slider {
  display: flex;
}

@keyframes rollSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}

.arrow-prev {
  border-bottom: 12px solid transparent;
  border-left: 16px solid #f0c434;
  border-right: none;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-prev-courses {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 15px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 270px;
  top: 0;
}

#arrow-prev-courses:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.arrow-next {
  border-bottom: 12px solid transparent;
  border-left: none;
  border-right: 16px solid #f0c434;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-next-courses {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 18px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 210px;
  top: 0;
}
#arrow-next-courses:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.ex-courses-front-title {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.ex-courses-front-title h2 {
  color: #faf8f0;
}

.ex-bt-courses-front:hover .icon-defult-ex-bt-courses-front {
  visibility: hidden;
}

.ex-courses-front-first {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.front-line {
  display: flex;
  justify-content: flex-end;
}

.splide__pagination .splide__pagination__page {
  background: #f0c434;
  height: 20px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}
.splide__pagination .splide__pagination__page.is-active {
  background: #6ddbe8;
  height: 27px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}

.ex-courses-front {
  position: relative;
}

.ex-courses-slider {
  margin-bottom: 70px;
}

.ex-button-courses {
  padding: 2px 10px;
}

@media (min-width: 368px) and (max-width: 767px) {
  #arrow-next-courses {
    display: none;
  }

  #arrow-prev-courses {
    display: none;
  }
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -56px;
  }

  .icon-defult-ex-bt-courses-front {
    width: 16px;
    height: 16px;
  }
  .ex-courses-front-title {
    margin-bottom: 30px;
  }
  .ex-courses-card-info ul {
    gap: 10px;
  }
  .ex-courses-card-info {
    gap: 54px;
  }
  .ex-courses-front-title {
    gap: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -60px;
  }
  #arrow-next-courses {
    display: none;
  }

  #arrow-prev-courses {
    display: none;
  }
  .ex-courses-card-info {
    gap: 46px;
  }
  .ex-courses-card-info ul {
    gap: 10px;
  }
}

/* subscription */

.ex-subscription-section {
  display: flex;
  gap: 214px;
  align-items: center;
  margin-bottom: 100px;
}
.subscription-details ul li {
  display: flex;
  gap: 8px;
  align-items: center;
}
.subscription-details {
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: flex-start;
}

.subscription-img-mobile {
  display: none;
}

@media (min-width: 368px) and (max-width: 767px) {
  .ex-subscription-section {
    gap: 20px;
    flex-direction: column;
  }
  .subscription-img {
    display: none;
  }
  .subscription-img-mobile {
    display: block;
  }
  .ex-bt-title {
    padding: 8px 20px;
  }
  .subscription-details ul li {
    align-items: flex-start;
  }
  .subscription-details ul li svg {
    margin-top: 6px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
}

/* products */

.hover-icon-ex-button-card-products {
  display: none;
  height: 30px;
  width: 30px;
  position: absolute;
  top: 50%;
  left: 0;
  z-index: 4;
  transform: translateY(-50%) translateX(0) rotate(0deg);
}

.ex-button:hover .hover-icon-ex-button-card-products {
  display: inline-block;
  animation: moveAndRotate 1s forwards;
}

@keyframes moveAndRotate {
  0% {
    left: 0;
    transform: translateY(-50%) translateX(0) rotate(0deg);
  }
  100% {
    left: 50%;
    transform: translateY(-50%) translateX(-50%) rotate(360deg);
  }
}

.ex-products-card {
  padding: 16px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  background: #040404;
  position: relative;
  z-index: 1;
}
.ex-products-card:before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(to right, #fbf5b7, #b38728);
  border-radius: inherit;
  mask: conic-gradient(#000 0 0) content-box exclude, conic-gradient(#000 0);
}

.ex-products-card-info h4 {
  color: #faf8f0;
}

.ex-products-front-title {
  display: flex;
  flex-direction: row;
  gap: 30px;
}

.ex-products-slider {
  display: flex;
}

.arrow-prev {
  border-bottom: 12px solid transparent;
  border-left: 16px solid #f0c434;
  border-right: none;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-prev-products {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 15px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 270px;
  top: 0;
}

#arrow-prev-products:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.arrow-next {
  border-bottom: 12px solid transparent;
  border-left: none;
  border-right: 16px solid #f0c434;
  border-top: 12px solid transparent;
  width: 0;
  height: 0;
}

#arrow-next-products {
  background: #6ddbe8;
  border-radius: 50px;
  padding: 6px 18px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid transparent;
  position: absolute;
  left: 210px;
  top: 0;
}
#arrow-next-products:hover {
  background: transparent;
  border-radius: 50px;
  border: 1px solid #6ddbe8;
}

.ex-products-front-title {
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}

.ex-products-front-title h2 {
  color: #faf8f0;
}

.ex-bt-products-front:hover .icon-defult-ex-bt-products-front {
  visibility: hidden;
}

.ex-products-front-first {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.front-line {
  display: flex;
  justify-content: flex-end;
}

.splide__pagination .splide__pagination__page {
  background: #f0c434;
  height: 20px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}
.splide__pagination .splide__pagination__page.is-active {
  background: #6ddbe8;
  height: 27px;
  width: 10px;
  border-radius: 18px;
  opacity: 1;
}

.ex-products-front {
  position: relative;
}

.ex-products-slider {
  margin-bottom: 70px;
}

.ex-button-products {
  padding: 9px 10px;
}

.ex-button-products-preview {
  padding: 9px 12px;
  border-radius: 50px;
  background: #333230;
  border: 1px solid transparent;
  z-index: 1;
  transition: transform 0.5s ease;
}
.ex-button-products-preview:hover {
  background: transparent;
  border: 1px solid #f0c434;
  transition: transform 0.5s ease;
}
.ex-button-products-preview span {
  font-size: 12px;
}
.ex-products-title-front {
  color: #f0c434;
}
.ex-products-type {
  display: flex;
  align-items: center;
  gap: 8px;
}
.ex-products-card-detalis {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;
  background: #141413;
  border-radius: 9px;
}
.ex-products-card-detalis p {
  line-height: 24px;
}
.ex-products-card-detalis div {
  background: #333230;
  border-radius: 2px;
  padding: 2px 8px;
  text-align: center;
}

.ex-products-price-front {
  color: #f0c434;
}

.ex-products-card-bt {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (min-width: 368px) and (max-width: 767px) {
  #arrow-next-products {
    display: none;
  }

  #arrow-prev-products {
    display: none;
  }
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -56px;
  }

  .icon-defult-ex-bt-products-front {
    width: 16px;
    height: 16px;
  }
  .ex-products-front-title {
    margin-bottom: 30px;
  }
  .ex-products-card-info ul {
    gap: 10px;
  }
  .ex-products-card-info {
    gap: 60px;
  }
  .ex-products-front-title {
    gap: 10px;
  }
  .ex-products-card-bt {
    gap: 35px;
  }
  .ex-button-products {
    gap: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  ul.splide__pagination.splide__pagination--rtl {
    bottom: -60px;
  }
  #arrow-next-products {
    display: none;
  }

  #arrow-prev-products {
    display: none;
  }
  .ex-products-card-info {
    gap: 46px;
  }
  .ex-products-card-info ul {
    gap: 10px;
  }
  .ex-products-card-detalis {
    justify-content: space-between;
  }
}

/* services */

.ex-services-box {
  padding: 20px 17px;
  display: flex;
  flex-direction: column;
  gap: 14px;
  /* background-image: url("/assets/img/Mask group.png"); */
  position: relative;
  z-index: 1;
  /* transform: rotate(45deg); */
  width: 273px;
  height: 273px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  transition: 0.5s ease;
  transform-origin: bottom right;
}

.ex-services-box:hover img.img-bg-services {
  filter: none;
  max-width: 200%;
  width: 530px !important;
}
.ex-services-box:hover {
  width: 366px;
  height: 366px;
  /* transform: scale(1.1); */
  transform: scale(1.1);
}

.ex-services-box .ex-bt-services {
  display: flex;
  align-items: center;
  transition: 0.5s ease;
  position: absolute;
  bottom: -170px;
}

.ex-services-box:hover .ex-bt-services {
  bottom: 24px;
}

.ex-services-box svg.ex-icon-hover-services {
  position: absolute;
  bottom: -210px;
  transition: 0.5s ease;
  margin-top: 36px;
}
.ex-services-box:hover svg.ex-icon-hover-services {
  bottom: -134px;
}
.ex-services-box-content {
  transform: rotate(-45deg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 14px;
  margin-top: -28px;
  margin-right: 28px;
}

.ex-services-box:before {
  content: "";
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(to right, #fbf5b7, #b38728);
  border-radius: inherit;
  mask: conic-gradient(#000 0 0) content-box exclude, conic-gradient(#000 0);
}

img.img-bg-services {
  transform: rotate(-45deg);
  position: absolute;
  z-index: -1;
  width: 387px !important;
  max-width: 150%;
  filter: blur(34px);
  transition: 0.3s ease;
}
.ex-services-title {
  text-align: center;
  transition: 0.5 ease;
}

.ex-services {
  transform: rotate(45deg);
  display: flex;
  gap: 80px;
  flex-direction: column;
  width: fit-content;
  margin-inline: auto;
  margin-top: 170px;
}
.ex-services-box:hover .ex-services{
  margin-top: 230px;
}
.ex-services-front {
  margin: 200px;
}
.group-box-services {
  display: flex;
  gap: 80px;
  flex-direction: row-reverse;
}

.ex-services-front-title h2 {
  color: #faf8f0;
}
.ex-services-front-title {
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.ex-services-front-first {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.ex-bt-services-front:hover .icon-defult-ex-bt-services-front {
  visibility: hidden;
}

.hover-icon-cat-services {
  transition: 1s ease;
}

.ex-services-box:hover .ex-services-title {
    transition: 0.5s ease;

}
.ex-services-box:hover .ex-services-title {
  transform: translateY(-166px);
}

.ex-services-box:hover .hover-icon-cat-services {
  transform: translateY(-324px);
  animation: opacityHover 0.6s;
}

@keyframes opacityHover {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 0;
  }
}

.circle-hover-services {
  position: absolute;
  opacity: 0;
  transition: 0.5s ease;
  top: -22px;
}
.ex-services-box:hover .circle-hover-services {
  opacity: 1;
}

@media (min-width: 368px) and (max-width: 767px) {
  .ex-services-front-title {
    margin-bottom: 30px;
  }
  .ex-services-front-title {
    gap: 16px;
  }
  .icon-defult-ex-bt-services-front {
    width: 16px;
    height: 16px;
  }
  .ex-services-front{
    margin: 0;
  }
  .ex-services{
    transform: rotate(0);
    flex-direction: column-reverse;
    margin-top: 140px;
    margin-bottom: 80px;
        gap: 180px;

  }
  .group-box-services{
    flex-direction: column;
    margin-right: 200px;
        gap: 180px;
  }
  .ex-services-box{
        transform: rotate(45deg);

  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .ex-services-front{
    margin: 0;
  }
  .ex-services{
    transform: rotate(0);
    flex-direction: column-reverse;
    margin-top: 140px;
    margin-bottom: 80px;
        gap: 180px;

  }
  .group-box-services{
    flex-direction: column;
    margin-right: 260px;
        gap: 180px;
  }
  .ex-services-box{
        transform: rotate(45deg);

  }
  .ex-services-box:hover{
    height: 340px;
    width: 340px;
  }
}
